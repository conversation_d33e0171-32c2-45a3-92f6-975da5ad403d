from __future__ import annotations
import async<PERSON>
import json
from typing import Optional, List, Any, Dict, cast
from contextlib import AsyncExitStack

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

from anthropic import Anthropic
from anthropic.types import Message<PERSON>aram, ToolParam
from mcp.types import TextContent

from dotenv import load_dotenv

load_dotenv()  # Load environment variables from .env file


class MCPClient:
    def __init__(self):
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.anthropic = Anthropic()

    async def connect(self, server_path: str):
        """Connect to MCP Server

        Args:
                server_path: Path to the server script (.py or .js)
        """
        is_python = server_path.endswith(".py")
        is_node = server_path.endswith(".js")

        if not (is_python or is_node):
            raise ValueError("Server script must be a .py or .js file")

        command = "python" if is_python else "node"
        server_params = StdioServerParameters(command=command, args=[server_path], env=None)

        stdio_transport = await self.exit_stack.enter_async_context(stdio_client(server_params))
        self.stdio, self.write = stdio_transport
        self.session = await self.exit_stack.enter_async_context(ClientSession(self.stdio, self.write))

        await self.session.initialize()

        # List available tools
        toolsData = await self.session.list_tools()
        tools = toolsData.tools
        print(f"\nAvailable tools: {[tool.name for tool in tools]}")

    async def disconnect(self):
        """Disconnect from MCP server"""
        if self.exit_stack:
            await self.exit_stack.aclose()

    async def query(self, query: str) -> str:
        """Process query using Claude and available tools"""
        messages: List[MessageParam] = [{"role": "user", "content": query}]

        if not self.session:
            raise RuntimeError("Not connected to MCP server. Call connect() first.")

        toolData = await self.session.list_tools()
        available_tools: List[ToolParam] = []
        for tool in toolData.tools:
            tool_param: ToolParam = {
                "name": tool.name,
                "description": tool.description or "",
                "input_schema": tool.inputSchema,
            }
            available_tools.append(tool_param)

        # Initialize Claude API call
        response = self.anthropic.messages.create(
            model="claude-3-5-sonnet-20241022", max_tokens=1000, messages=messages, tools=available_tools
        )

        final_text = []

        # Process the response content
        if response.content:
            assistant_content = []

            for content in response.content:
                if content.type == "text":
                    final_text.append(content.text)
                    assistant_content.append({"type": "text", "text": content.text})
                elif content.type == "tool_use":
                    tool_name = content.name
                    # Convert the input object to a dictionary
                    # The Anthropic API returns content.input as a dict-like object
                    tool_args: Dict[str, Any] = cast(Dict[str, Any], content.input) if content.input else {}

                    result = await self.session.call_tool(tool_name, tool_args)
                    final_text.append(f"[Calling tool {tool_name} with arguments {json.dumps(tool_args)}]")

                    # Add the tool use to assistant content
                    assistant_content.append(
                        {"type": "tool_use", "id": content.id, "name": tool_name, "input": tool_args}
                    )

                    # Add assistant message with tool use
                    messages.append({"role": "assistant", "content": assistant_content})

                    # Add user message with tool result
                    # Convert MCP result content to string for Anthropic API
                    result_content = ""
                    if hasattr(result, "content") and result.content:
                        # Handle list of content blocks from MCP
                        if isinstance(result.content, list):
                            for content_block in result.content:
                                if isinstance(content_block, TextContent):
                                    result_content += content_block.text
                                else:
                                    result_content += str(content_block)
                        else:
                            result_content = str(result.content)
                    else:
                        result_content = str(result)

                    messages.append(
                        {
                            "role": "user",
                            "content": [{"type": "tool_result", "tool_use_id": content.id, "content": result_content}],
                        }
                    )

                    # Get Claude's response to the tool result
                    follow_up_response = self.anthropic.messages.create(
                        model="claude-3-5-sonnet-20241022", max_tokens=1000, messages=messages, tools=available_tools
                    )

                    # Add the follow-up response to final text
                    if follow_up_response.content:
                        for follow_up_content in follow_up_response.content:
                            if follow_up_content.type == "text":
                                final_text.append(follow_up_content.text)

        return "\n".join(final_text)

    async def chat(self):
        """Interactive Chat Loop"""
        print("\nMCP Client Started!")
        print("\nType your queries or 'quit' to exit.")

        while True:
            try:
                query = input("> ").strip()
                if query.lower() == "quit":
                    break
                response = await self.query(query)
                print("Claude's response:")
                print("\n" + response)
            except Exception as e:
                print(f"\nError: {str(e)}")


# Example usage
async def main():
    """Example usage of the MCPClient with Claude"""
    if len(sys.argv) > 2:
        print("Usage: python client_claude.py <path_to_server>")
        sys.exit(1)

    client = MCPClient()

    try:
        # Connect to your MCP server
        await client.connect(server_path=sys.argv[1])
        await client.chat()

        # # Query Claude with access to MCP tools
        # response = await client.query("What tools are available and can you use one of them?")
        # print("Claude's response:")
        # print(response)

    except Exception as e:
        print(f"Error: {e}")
    finally:
        await client.disconnect()


if __name__ == "__main__":
    import sys

    asyncio.run(main())
